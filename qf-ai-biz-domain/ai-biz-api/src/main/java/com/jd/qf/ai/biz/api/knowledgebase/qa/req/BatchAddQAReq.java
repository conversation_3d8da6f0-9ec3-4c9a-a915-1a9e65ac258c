package com.jd.qf.ai.biz.api.knowledgebase.qa.req;

import com.jd.qf.ai.biz.api.knowledgebase.qa.dto.AddQAReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量添加QA请求
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Data
public class BatchAddQAReq {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不得为空")
    private String projectId;

    /**
     * QA列表
     */
    @NotEmpty(message = "QA列表不能为空")
    @Valid
    private List<AddQAReq> qaList;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
