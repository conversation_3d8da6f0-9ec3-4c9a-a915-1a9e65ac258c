package com.jd.qf.ai.biz.api.knowledgebase.qa;

import com.jd.qf.ai.biz.api.knowledgebase.qa.dto.*;
import com.jd.qf.ai.biz.api.knowledgebase.qa.req.BatchAddQAReq;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;

/**
 * 知识库门面
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
public interface KnowledgeBaseFacade {

    /**
     * 创建知识库
     */
    BizResponse<Void> createKnowledgeBase(CreateKnowledgeBaseReq req);

    /**
     * 添加单条QA
     */
    BizResponse<Void> addQA(AddQAReq req);

    /**
     * 批量添加QA
     */
    BizResponse<Void> batchAddQA(BatchAddQAReq req);

    /**
     * 查询QA详情
     */
    BizResponse<QADetailResp> queryQADetail(QueryQADetailReq req);

    /**
     * 编辑QA
     */
    BizResponse<Void> editQA(EditQAReq req);

    /**
     * 导入QA
     */
    BizResponse<Void> importQA(ImportQAReq req);

    /**
     * 批量导出
     */
    BizResponse<ExportQAResp> exportQA(PageQueryQAReq req);

    /**
     * 分页搜索
     */
    BizResponse<PageResult<PageQueryQAResp>> pageQuery(PageQueryQAReq req);

    /**
     * 批量移动分组
     */
    BizResponse<Void> batchMoveQa(BatchMoveQaReq req);

    /**
     * 批量删除QA
     */
    BizResponse<Void> batchDeleteQa(BatchDeleteQaReq req);

}
