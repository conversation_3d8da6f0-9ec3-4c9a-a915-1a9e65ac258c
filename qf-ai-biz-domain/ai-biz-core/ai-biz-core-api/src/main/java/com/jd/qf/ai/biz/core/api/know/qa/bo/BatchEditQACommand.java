package com.jd.qf.ai.biz.core.api.know.qa.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量编辑QA请求
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchEditQACommand {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不得为空")
    private String projectId;

    /**
     * QA列表
     */
    @NotEmpty(message = "QA列表不能为空")
    @Valid
    private List<EditQACommand> qaList;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
