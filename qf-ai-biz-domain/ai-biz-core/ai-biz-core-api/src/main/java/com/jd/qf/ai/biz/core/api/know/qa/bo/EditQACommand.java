package com.jd.qf.ai.biz.core.api.know.qa.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * 添加QA请求
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EditQACommand {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不得为空")
    private String projectId;
    /**
     * QA编码
     */
    @NotBlank(message = "QA编码不得为空")
    private String qaNo;

    /**
     * 分组编码
     */
    @NotBlank(message = "分组编码不得为空")
    private String groupNo;

    /**
     * 问题
     */
    @NotBlank(message = "问题不得为空")
    private String question;

    /**
     * 相似问题列表
     */
    private List<String> similarQuestionList = new ArrayList<>();

    /**
     * 答案
     */
    @NotBlank(message = "答案不得为空")
    private String answer;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
