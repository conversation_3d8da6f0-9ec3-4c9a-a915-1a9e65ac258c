package com.jd.qf.ai.biz.core.service.know.qa;

import com.jd.qf.ai.biz.core.api.know.qa.bo.AddQACommand;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * QA处理数据
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Data
@Builder
public class QAProcessData {
    
    /**
     * 添加QA命令
     */
    private AddQACommand addQACommand;
    
    /**
     * 问题MD5
     */
    private String questionMd5;
    
    /**
     * 相似问题MD5列表
     */
    private List<String> similarQuestionMd5List;
    
    /**
     * QA实体对象
     */
    private AiKnowQaPo qaPo;
    
    /**
     * 外部问题ID
     */
    private String externalQuestionId;
    
    /**
     * 外部QA ID
     */
    private String externalQaId;
}
