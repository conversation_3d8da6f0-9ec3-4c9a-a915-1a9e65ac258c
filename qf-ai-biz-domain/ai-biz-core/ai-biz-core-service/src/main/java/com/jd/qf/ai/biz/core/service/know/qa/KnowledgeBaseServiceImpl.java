package com.jd.qf.ai.biz.core.service.know.qa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.biz.core.api.know.qa.KnowledgeBaseService;
import com.jd.qf.ai.biz.core.api.know.qa.bo.*;
import com.jd.qf.ai.biz.core.converter.Converter;
import com.jd.qf.ai.biz.core.service.know.qa.bo.KnowledgeBaseIdConfig;
import com.jd.qf.ai.biz.core.service.know.qa.bo.QARow;
import com.jd.qf.ai.biz.core.service.know.qa.excel.CountExcelListener;
import com.jd.qf.ai.biz.core.service.know.qa.excel.ValidateQaListener;
import com.jd.qf.ai.biz.core.service.oss.OssService;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.*;
import com.jd.qf.ai.biz.infrastructure.dao.po.*;
import com.jd.qf.ai.server.common.lang.pageutil.DBPageUtils;
import com.jd.qf.ai.server.common.lang.util.FileUtil;
import com.jd.qf.ai.server.common.pojo.enums.dify.*;
import com.jd.qf.ai.server.common.pojo.enums.DeDuplicateEnum;
import com.jd.qf.ai.server.common.pojo.enums.KnowContentTypeEnum;
import com.jd.qf.ai.server.common.pojo.enums.KnowTypeEnum;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import com.jd.qf.ai.server.common.pojo.result.ResponseEnum;
import com.jd.qf.ai.server.common.pojo.utils.SequenceNoUtils;
import com.jdt.open.capability.log.annotation.OpenLog;
import com.jdt.open.exception.BizException;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.model.datasets.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMAT;

/**
 * 知识库服务实现
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Slf4j
@Service
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Autowired
    private AiKnowConfigMapper aiKnowConfigMapper;
    @Autowired
    private AiKnowGroupMapper aiKnowGroupMapper;
    @Autowired
    private AiKnowQaMapper aiKnowQaMapper;
    @Autowired
    private AiKnowSimilarQuestionMapper aiKnowSimilarQuestionMapper;
    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private OssService ossService;
    @Autowired
    private DifyDatasetsClient difyDatasetsClient;
    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 问题前缀
     */
    private static final String QUESTION_PREFIX = "问题:";

    /**
     * 答案前缀
     */
    private static final String ANSWER_PREFIX = "答案:";

    /**
     * 换行符
     */
    private static final String SWITCH_LINE = "\n";


    @Override
    @OpenLog("创建空白知识库")
    public void createKnowledgeBase(CreateKnowledgeBaseCommand req) {
        ProjectPo projectPo = getProjectPo(req.getProjectId());

        List<AiKnowConfigPo> aiKnowConfigPos = aiKnowConfigMapper.selectByProjectId(req.getProjectId());
        if (CollectionUtil.isNotEmpty(aiKnowConfigPos)) {
            log.warn("知识库已创建,无需重复创建");
            return;
        }
        //创建问题知识库
        doCreateKnowledgeBase(req, projectPo, KnowContentTypeEnum.QUESTION);

        //创建QA库
        doCreateKnowledgeBase(req, projectPo, KnowContentTypeEnum.QA);

    }

    /**
     * 创建知识库
     *
     * @param req                 创建知识库命令
     * @param projectPo           项目信息
     * @param knowContentTypeEnum 知识内容类型枚举
     */
    private void doCreateKnowledgeBase(CreateKnowledgeBaseCommand req, ProjectPo projectPo, KnowContentTypeEnum knowContentTypeEnum) {
        DatasetResponse dataset;
        try {
            CreateDatasetRequest createDatasetRequest = new CreateDatasetRequest();
            createDatasetRequest.setName(projectPo.getProjectName() + knowContentTypeEnum.getMsg());
            createDatasetRequest.setIndexingTechnique(IndexTechniqueEnum.HIGH_QUALITY.getCode());
            createDatasetRequest.setPermission(DifyPermissionEnum.ALL.getCode());
            dataset = difyDatasetsClient.createDataset(createDatasetRequest);
        } catch (Exception e) {
            log.error("调用Dify RPC创建知识库失败,入参{}", JSON.toJSONString(req), e);
            throw new BizException("创建知识库失败,请联系研发人员解决");
        }

        String externalKnowId = dataset.getId();
        String contentType = knowContentTypeEnum.getCode();

        AiKnowConfigPo aiKnowConfigPo = new AiKnowConfigPo();
        aiKnowConfigPo.setConfigNo(SequenceNoUtils.getSequenceNo());
        aiKnowConfigPo.setProjectId(req.getProjectId());
        aiKnowConfigPo.setProjectName(projectPo.getProjectName());
        aiKnowConfigPo.setKnowType(KnowTypeEnum.DIFY.getCode());
        aiKnowConfigPo.setContentType(contentType);
        aiKnowConfigPo.setExternalKnowId(externalKnowId);
        aiKnowConfigPo.setCreator(req.getOperator());
        aiKnowConfigMapper.insert(aiKnowConfigPo);
    }

    private ProjectPo getProjectPo(String projectId) {
        ProjectPo projectPo = projectMapper.queryByProjectId(projectId);
        if (projectPo == null) {
            log.warn("项目不存在");
            throw new BizException("项目不存在");
        }
        return projectPo;
    }

    @Override
    @OpenLog("添加单条QA")
    public void addQA(AddQACommand req) {
        // 将单条请求转换为批量请求，复用批量逻辑
        BatchAddQACommand batchCommand = BatchAddQACommand.builder()
                .projectId(req.getProjectId())
                .qaList(List.of(req))
                .operator(req.getOperator())
                .build();
        batchAddQA(batchCommand);
    }

    @Override
    @OpenLog("批量添加QA")
    public void batchAddQA(BatchAddQACommand req) {
        if (CollectionUtil.isEmpty(req.getQaList())) {
            log.warn("批量添加QA列表为空");
            return;
        }

        ProjectPo projectPo = getProjectPo(req.getProjectId());

        // 1. 批量校验和预处理
        List<QAProcessData> processDataList = new ArrayList<>();
        List<String> allQuestionMd5List = new ArrayList<>();
        List<String> allSimilarQuestionMd5List = new ArrayList<>();

        for (AddQACommand qaCommand : req.getQaList()) {
            String questionMd5 = SecureUtil.md5(qaCommand.getQuestion());
            allQuestionMd5List.add(questionMd5);

            List<String> similarMd5List = qaCommand.getSimilarQuestionList().stream()
                    .map(SecureUtil::md5)
                    .toList();
            allSimilarQuestionMd5List.addAll(similarMd5List);

            QAProcessData processData = QAProcessData.builder()
                    .addQACommand(qaCommand)
                    .questionMd5(questionMd5)
                    .similarQuestionMd5List(similarMd5List)
                    .build();
            processDataList.add(processData);
        }

        // 2. 批量校验重复问题
        batchValidateQuestions(req.getProjectId(), allQuestionMd5List, allSimilarQuestionMd5List);

        // 3. 构建批量插入数据
        List<AiKnowQaPo> qaPoList = new ArrayList<>();
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = new ArrayList<>();

        for (QAProcessData processData : processDataList) {
            AddQACommand qaCommand = processData.getAddQACommand();
            AiKnowQaPo qaPo = buildInsertQaPo(qaCommand, projectPo, qaCommand.getQuestion(), processData.getQuestionMd5());
            qaPoList.add(qaPo);

            // 构建相似问题数据
            for (String similarQuestion : qaCommand.getSimilarQuestionList()) {
                AiKnowSimilarQuestionPo similarPo = new AiKnowSimilarQuestionPo();
                similarPo.setSimilarQuestionNo(SequenceNoUtils.getSequenceNo());
                similarPo.setProjectId(req.getProjectId());
                similarPo.setQaNo(qaPo.getQaNo());
                similarPo.setSimilarQuestion(similarQuestion);
                similarPo.setSimilarQuestionMd5(SecureUtil.md5(similarQuestion));
                similarPo.setCreator(req.getOperator());
                similarQuestionPoList.add(similarPo);
            }

            processData.setQaPo(qaPo);
        }

        // 4. 批量数据库操作
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            log.info("批量添加QA到本地数据库,数量{}", qaPoList.size());
            if (CollectionUtil.isNotEmpty(qaPoList)) {
                aiKnowQaMapper.batchInsert(qaPoList);
            }
            if (CollectionUtil.isNotEmpty(similarQuestionPoList)) {
                aiKnowSimilarQuestionMapper.batchInsert(similarQuestionPoList);
            }
        });

        // 5. 批量添加到远程知识库
        batchAddToRemoteKnowledge(req.getProjectId(), processDataList);
    }

    private void insert2LocalSimilarQuestion(List<String> similarQuestionList, AiKnowQaPo qaPo, String projectId) {
        similarQuestionList.forEach(similarQ -> {
            AiKnowSimilarQuestionPo record = new AiKnowSimilarQuestionPo();
            record.setSimilarQuestionNo(SequenceNoUtils.getSequenceNo());
            record.setProjectId(projectId);
            record.setQaNo(qaPo.getQaNo());
            record.setSimilarQuestion(similarQ);
            record.setSimilarQuestionMd5(SecureUtil.md5(similarQ));
            aiKnowSimilarQuestionMapper.insert(record);
        });
    }


    private AiKnowQaPo buildInsertQaPo(AddQACommand req, ProjectPo projectPo, String question, String questionMd5) {
        AiKnowQaPo qaPo = new AiKnowQaPo();
        qaPo.setGroupNo(req.getGroupNo());
        qaPo.setQaNo(SequenceNoUtils.getSequenceNo());
        qaPo.setProjectId(req.getProjectId());
        qaPo.setProjectName(projectPo.getProjectName());
        qaPo.setQuestion(question);
        qaPo.setQuestionMd5(questionMd5);
        qaPo.setAnswer(req.getAnswer());
        qaPo.setCreator(req.getOperator());
        return qaPo;
    }

    private void validateQuestion(AddQACommand req, String questionMd5, List<String> similarQuestionList) {
        AiKnowQaPo aiKnowQaPo = aiKnowQaMapper.selectByProjectIdAndQuestionMd5(req.getProjectId(), questionMd5);
        if (aiKnowQaPo != null) {
            log.warn("问题已存在");
            throw new BizException("问题已存在");
        }
        if (CollectionUtil.isNotEmpty(similarQuestionList)) {
            List<String> similarMd5List = similarQuestionList.stream().map(SecureUtil::md5).toList();
            List<AiKnowSimilarQuestionPo> aiKnowSimilarQuestionPos = aiKnowSimilarQuestionMapper.selectByQaNoAndMd5(req.getProjectId(), similarMd5List);
            if (CollectionUtil.isNotEmpty(aiKnowSimilarQuestionPos)) {
                log.warn("相似问题已存在");
                throw new BizException("相似问题" + aiKnowSimilarQuestionPos.get(0).getSimilarQuestion() + "已存在");
            }
        }
    }

    /**
     * 批量校验问题重复
     */
    private void batchValidateQuestions(String projectId, List<String> questionMd5List, List<String> similarQuestionMd5List) {
        // 批量查询主问题是否重复
        if (CollectionUtil.isNotEmpty(questionMd5List)) {
            List<AiKnowQaPo> existingQAs = aiKnowQaMapper.selectByProjectIdAndMd5List(projectId, questionMd5List);
            if (CollectionUtil.isNotEmpty(existingQAs)) {
                log.warn("存在重复的主问题");
                throw new BizException("问题已存在: " + existingQAs.get(0).getQuestion());
            }
        }

        // 批量查询相似问题是否重复
        if (CollectionUtil.isNotEmpty(similarQuestionMd5List)) {
            List<AiKnowSimilarQuestionPo> existingSimilarQuestions = aiKnowSimilarQuestionMapper.selectByQaNoAndMd5(projectId, similarQuestionMd5List);
            if (CollectionUtil.isNotEmpty(existingSimilarQuestions)) {
                log.warn("存在重复的相似问题");
                throw new BizException("相似问题已存在: " + existingSimilarQuestions.get(0).getSimilarQuestion());
            }
        }
    }

    private KnowledgeBaseIdConfig getAiKnowConfigPos(String projectId) {
        List<AiKnowConfigPo> aiKnowConfigPos = aiKnowConfigMapper.selectByProjectId(projectId);
        if (CollectionUtil.isEmpty(aiKnowConfigPos)) {
            log.error("项目下知识库配置为空,入参{}", JSON.toJSONString(projectId));
            throw new BizException("项目下不存在任何知识库配置");
        }
        KnowledgeBaseIdConfig config = new KnowledgeBaseIdConfig();

        AiKnowConfigPo questionConfig = aiKnowConfigPos.stream()
                .filter(po -> KnowContentTypeEnum.QUESTION.getCode().equals(po.getContentType()))
                .findAny()
                .orElse(new AiKnowConfigPo());
        config.setQuestionId(questionConfig.getExternalKnowId());

        AiKnowConfigPo answerConfig = aiKnowConfigPos.stream()
                .filter(po -> KnowContentTypeEnum.QA.getCode().equals(po.getContentType()))
                .findAny()
                .orElse(new AiKnowConfigPo());
        config.setQaId(answerConfig.getExternalKnowId());

        return config;
    }

    /**
     * 将问答内容添加到知识库
     *
     * @param req                 添加问答的命令对象
     * @param knowledgeBaseId     知识库ID配置
     * @param externalQuestion    外部问题内容
     * @param knowContentTypeEnum 知识内容类型枚举
     */
    private String addDocument2Knowledge(AddQACommand req, String knowledgeBaseId, StringBuilder externalQuestion, KnowContentTypeEnum knowContentTypeEnum) {

        if (StrUtil.isBlank(knowledgeBaseId)) {
            log.error("项目下不存在知识库配置,入参{}", JSON.toJSONString(req));
            throw new BizException("项目下不存在知识库配置");
        }
        //添加到远程问题库
        try {
            CreateDocumentByTextRequest textRequest = CreateDocumentByTextRequest.builder()
                    .name(req.getQuestion())
                    .text(externalQuestion.toString())
                    .indexingTechnique(IndexTechniqueEnum.HIGH_QUALITY.getCode())
                    .docForm(DocFormEnum.TEXT_MODEL.getCode())
                    .docLanguage(DocLanguageEnum.CHINESE.getCode())
                    .processRule(ProcessRule.builder()
                            .mode(ModeEnum.CUSTOM.getCode())
                            .rules(ProcessRule.Rules.builder()
                                    .preProcessingRules(new ArrayList<>())
                                    .segmentation(ProcessRule.Segmentation.builder().maxTokens(4000).separator("\n\n").build())
                                    .build())
                            .build())
                    //todo 换成DUCC配置的模型
                    .embeddingModel("bge-base-zh-v1.5")
                    .embeddingModelProvider("langgenius/xinference/xinference")
                    .retrievalModel(RetrievalModel.builder()
                            .searchMethod("hybrid_search")
                            .topK(2)
                            .rerankingModel(RetrievalModel.RerankingModel.builder()
                                    .rerankingModelName("bge-reranker-v2-m3")
                                    .rerankingProviderName("langgenius/xinference/xinference")
                                    .build())
                            .rerankingEnable(true)
                            .scoreThresholdEnabled(true)
                            .scoreThreshold(0.4f)
                            .build())
                    .build();
            DocumentResponse response = difyDatasetsClient.createDocumentByText(knowledgeBaseId, textRequest);
            return response.getDocument().getId();
        } catch (Exception e) {
            log.error("添加QA到远程知识库失败,入参{}", JSON.toJSONString(req), e);
            throw new BizException("添加问题到远程知识库失败");
        }
    }

    @Override
    @OpenLog("查询QA详情")
    public QADetailBo queryQADetail(QueryQADetailQuery req) {

        AiKnowQaPo po = getAiKnowQaPo(req.getProjectId(), req.getQaNo());
        AiKnowGroupPo aiKnowGroupPo = getAiKnowGroupPo(req.getProjectId(), po.getGroupNo());

        List<AiKnowSimilarQuestionPo> similarQuestionPoList = aiKnowSimilarQuestionMapper.selectByQaNo(po.getQaNo());
        QADetailBo bo = Converter.INSTANCE.to(po);
        List<String> similarList = similarQuestionPoList.stream().map(AiKnowSimilarQuestionPo::getSimilarQuestion).toList();
        bo.setSimilarQuestionList(similarList);
        bo.setGroupName(aiKnowGroupPo.getGroupName());
        return bo;
    }

    private AiKnowGroupPo getAiKnowGroupPo(String projectId, String groupNo) {
        AiKnowGroupPo aiKnowGroupPo = aiKnowGroupMapper.selectByGroupNo(projectId, groupNo);
        if (aiKnowGroupPo == null) {
            log.warn("分组不存在");
            throw new BizException("分组不存在");
        }
        return aiKnowGroupPo;
    }

    private AiKnowQaPo getAiKnowQaPo(String projectId, String qaNo) {
        AiKnowQaPo po = aiKnowQaMapper.selectByProjectIdAndQaNo(projectId, qaNo);
        if (po == null) {
            log.warn("QA不存在");
            throw new BizException("问答话术不存在");
        }
        return po;
    }

    @Override
    @OpenLog("编辑单条QA")
    public void editQA(EditQACommand req) {
        // 将单条请求转换为批量请求，复用批量逻辑
        BatchEditQACommand batchCommand = BatchEditQACommand.builder()
                .projectId(req.getProjectId())
                .qaList(List.of(req))
                .operator(req.getOperator())
                .build();
        batchEditQA(batchCommand);
    }

    @Override
    @OpenLog("批量编辑QA")
    public void batchEditQA(BatchEditQACommand req) {
        if (CollectionUtil.isEmpty(req.getQaList())) {
            log.warn("批量编辑QA列表为空");
            return;
        }

        // 1. 批量查询现有QA信息
        List<String> qaNoList = req.getQaList().stream()
                .map(EditQACommand::getQaNo)
                .toList();
        List<AiKnowQaPo> existingQaList = aiKnowQaMapper.selectByQaNoList(req.getProjectId(), qaNoList);
        Map<String, AiKnowQaPo> existingQaMap = existingQaList.stream()
                .collect(Collectors.toMap(AiKnowQaPo::getQaNo, qa -> qa));

        // 2. 构建批量更新数据
        List<AiKnowQaPo> updatePoList = new ArrayList<>();
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = new ArrayList<>();
        List<QAEditProcessData> processDataList = new ArrayList<>();

        for (EditQACommand editCommand : req.getQaList()) {
            AiKnowQaPo existingQa = existingQaMap.get(editCommand.getQaNo());
            if (existingQa == null) {
                log.warn("QA不存在，跳过编辑: {}", editCommand.getQaNo());
                continue;
            }

            // 构建更新对象
            AiKnowQaPo updatePo = Converter.INSTANCE.to(editCommand);
            updatePo.setModifier(req.getOperator());
            updatePo.setModifiedTime(new Date());
            updatePoList.add(updatePo);

            // 构建相似问题数据
            List<String> similarQuestionList = editCommand.getSimilarQuestionList();
            if (CollectionUtil.isNotEmpty(similarQuestionList)) {
                for (String similarQuestion : similarQuestionList) {
                    AiKnowSimilarQuestionPo similarPo = new AiKnowSimilarQuestionPo();
                    similarPo.setSimilarQuestionNo(SequenceNoUtils.getSequenceNo());
                    similarPo.setProjectId(req.getProjectId());
                    similarPo.setQaNo(editCommand.getQaNo());
                    similarPo.setSimilarQuestion(similarQuestion);
                    similarPo.setSimilarQuestionMd5(SecureUtil.md5(similarQuestion));
                    similarPo.setCreator(req.getOperator());
                    similarQuestionPoList.add(similarPo);
                }
            }

            // 构建远程更新数据
            QAEditProcessData processData = QAEditProcessData.builder()
                    .editCommand(editCommand)
                    .existingQa(existingQa)
                    .build();
            processDataList.add(processData);
        }

        // 3. 批量数据库操作
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            log.info("批量编辑QA到本地数据库,数量{}", updatePoList.size());

            // 批量删除旧的相似问题
            if (CollectionUtil.isNotEmpty(qaNoList)) {
                aiKnowSimilarQuestionMapper.logicDeleteByQaNo(qaNoList);
            }

            // 批量更新QA
            for (AiKnowQaPo updatePo : updatePoList) {
                aiKnowQaMapper.updateById(updatePo);
            }

            // 批量插入新的相似问题
            if (CollectionUtil.isNotEmpty(similarQuestionPoList)) {
                aiKnowSimilarQuestionMapper.batchInsert(similarQuestionPoList);
            }
        });

        // 4. 批量更新远程知识库
        batchUpdateRemoteKnowledge(req.getProjectId(), processDataList);
    }

    @Override
    @OpenLog("导入QA")
    public void importQA(ImportQACommand req) {

        String filePath = "/export/importFile/" + req.getProjectId() + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        File importFile = null;
        try {
            FileUtil.downloadUrlToFile(req.getFileUrl(), filePath);
            importFile = new File(filePath);

            //统计行数,校验是否超限制
            EasyExcel.read(importFile, QARow.class, new CountExcelListener())
                    .excelType(ExcelTypeEnum.XLSX)
                    .headRowNumber(7)
                    .sheet().doRead();

            //校验各个字段
            ValidateQaListener validateQaListener = new ValidateQaListener();
            EasyExcel.read(importFile, QARow.class, validateQaListener)
                    .excelType(ExcelTypeEnum.XLSX)
                    .headRowNumber(7)
                    .sheet().doRead();
            List<String> errorMessages = validateQaListener.getErrorMessages();
            if (!CollectionUtil.isEmpty(errorMessages)) {
                log.warn("导入QA失败,错误信息{}", JSON.toJSONString(errorMessages));
                throw new BizException(ResponseEnum.PARAM_ERROR.getCode(), "导入QA失败,错误信息:" + JSON.toJSONString(errorMessages));
            }

            //查询项目名称
            ProjectPo projectPo = getProjectPo(req.getProjectId());

            Map<String, List<QARow>> groupIdmap = processGroup(req, validateQaListener.getGroupMap(), projectPo);
            //主问题查重,重复的覆盖或者跳过;相似问题不查重
            List<String> questionMd5List = validateQaListener.getQuestionSet().stream().map(SecureUtil::md5).toList();
            Map<String, String> questionAndIdMap = aiKnowQaMapper.selectByProjectIdAndMd5List(req.getProjectId(), questionMd5List)
                    .stream().collect(Collectors.toMap(AiKnowQaPo::getQuestion, AiKnowQaPo::getQaNo));

            // 根据去重策略处理重复问题
            DeDuplicateEnum deDuplicateEnum = DeDuplicateEnum.getByCode(req.getDeDuplicateType());
            if (deDuplicateEnum == null) {
                throw new BizException("不支持的去重策略: " + req.getDeDuplicateType());
            }

            groupIdmap.forEach((groupNo, qaList) -> {
                List<AddQACommand> batchAddCommands = new ArrayList<>();
                List<EditQACommand> batchEditCommands = new ArrayList<>();

                for (QARow qa : qaList) {
                    String existingQaNo = questionAndIdMap.get(qa.getQuestion());

                    if (existingQaNo != null) {
                        // 问题已存在，根据策略处理
                        if (deDuplicateEnum == DeDuplicateEnum.COVER) {
                            // 覆盖策略：更新现有问题
                            EditQACommand editCommand = EditQACommand.builder()
                                    .projectId(req.getProjectId())
                                    .qaNo(existingQaNo)
                                    .groupNo(groupNo)
                                    .question(qa.getQuestion())
                                    .answer(qa.getAnswer())
                                    .similarQuestionList(qa.getSimilarQuestionList())
                                    .operator(req.getOperator())
                                    .build();
                            batchEditCommands.add(editCommand);
                        }
                    } else {
                        // 新问题，添加到批量添加列表
                        AddQACommand addCommand = AddQACommand.builder()
                                .projectId(req.getProjectId())
                                .question(qa.getQuestion())
                                .answer(qa.getAnswer())
                                .groupNo(groupNo)
                                .similarQuestionList(qa.getSimilarQuestionList())
                                .operator(req.getOperator())
                                .build();
                        batchAddCommands.add(addCommand);
                    }
                }

                // 批量添加新问题
                if (CollectionUtil.isNotEmpty(batchAddCommands)) {
                    BatchAddQACommand batchAddCommand = BatchAddQACommand.builder()
                            .projectId(req.getProjectId())
                            .qaList(batchAddCommands)
                            .operator(req.getOperator())
                            .build();
                    this.batchAddQA(batchAddCommand);
                    log.info("批量导入分组 {} 新增QA数量: {}", groupNo, batchAddCommands.size());
                }

                // 批量更新重复问题
                if (CollectionUtil.isNotEmpty(batchEditCommands)) {
                    BatchEditQACommand batchEditCommand = BatchEditQACommand.builder()
                            .projectId(req.getProjectId())
                            .qaList(batchEditCommands)
                            .operator(req.getOperator())
                            .build();
                    this.batchEditQA(batchEditCommand);
                    log.info("批量导入分组 {} 覆盖QA数量: {}", groupNo, batchEditCommands.size());
                }
            });


        } catch (BizException e) {
            log.warn("导入QA-业务异常", e);
            throw e;
        } catch (ExcelDataConvertException e) {
            log.warn("导入QA-格式转换异常", e);
            throw new BizException(ResponseEnum.PARAM_ERROR.getCode(), "导入QA失败-格式转换异常");
        } catch (Exception e) {
            log.error("导入QA-未知异常", e);
            throw new BizException(ResponseEnum.PARAM_ERROR.getCode(), "导入QA失败-未知异常");
        } finally {
            if (importFile != null) {
                boolean delete = importFile.delete();
                log.info("删除导入QA文件结果:{}", delete);
            }
        }
    }

    private Map<String, List<QARow>> processGroup(ImportQACommand req, Map<String, List<QARow>> groupMap, ProjectPo projectPo) {

        //批量查询分组,得到分组ID,如果分组不存在,则要创建分组
        List<String> groupNameList = new ArrayList<>(groupMap.keySet());
        //已存在的分组名称和ID的映射
        Map<String, String> existGroupIdNameMap = aiKnowGroupMapper.selectByGroupNames(req.getProjectId(), groupNameList)
                .stream().collect(Collectors.toMap(AiKnowGroupPo::getGroupName, AiKnowGroupPo::getGroupNo));
        //找到不存在的分组名称,并创建这些分组
        groupNameList.stream()
                .filter(group -> !existGroupIdNameMap.containsKey(group))
                .forEach(newGroup -> {
                    String sequenceNo = SequenceNoUtils.getSequenceNo();
                    AiKnowGroupPo aiKnowGroupPo = new AiKnowGroupPo();
                    aiKnowGroupPo.setGroupNo(sequenceNo);
                    aiKnowGroupPo.setProjectId(req.getProjectId());
                    aiKnowGroupPo.setProjectName(projectPo.getProjectName());
                    aiKnowGroupPo.setGroupName(newGroup);
                    aiKnowGroupPo.setCreator(req.getOperator());
                    aiKnowGroupMapper.insert(aiKnowGroupPo);
                    existGroupIdNameMap.put(newGroup, sequenceNo);
                });
        //将分组名-qa列表的映射转为分组ID-qa列表
        Map<String, List<QARow>> groupIdMap = new HashMap<>();
        groupMap.forEach((groupName, qaList) -> {
            String groupId = existGroupIdNameMap.get(groupName);
            groupIdMap.put(groupId, qaList);
        });
        return groupIdMap;
    }

    @Override
    @OpenLog("导出QA")
    public ExportQABo exportQA(PageQAQuery req) {
        req.setPageNum(1);
        req.setPageSize(10000);
        PageResult<PageQueryQABo> pageResult = this.pageQuery(req);
        List<PageQueryQABo> qaBoList = pageResult.getList();
        if (CollectionUtil.isEmpty(qaBoList)) {
            log.warn("待导出数据为空");
            throw new BizException("待导出数据为空");
        }

        //查询相似问题列表
        List<String> qaNoList = qaBoList.stream().map(PageQueryQABo::getQaNo).toList();
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = aiKnowSimilarQuestionMapper.selectByQaList(req.getProjectId(), qaNoList);
        Map<String, List<AiKnowSimilarQuestionPo>> similarMap = similarQuestionPoList.stream().collect(Collectors.groupingBy(AiKnowSimilarQuestionPo::getQaNo));

        //查询分组名称
        List<AiKnowGroupPo> aiKnowGroupPos = aiKnowGroupMapper.selectByProjectId(req.getProjectId());
        Map<String, String> groupMap = aiKnowGroupPos.stream().collect(Collectors.toMap(AiKnowGroupPo::getGroupNo, AiKnowGroupPo::getGroupName));

        //组装行数据
        List<QARow> rowList = qaBoList.stream().map(qa -> {
            QARow row = new QARow();
            row.setGroupName(groupMap.get(qa.getGroupNo()));
            row.setQuestion(qa.getQuestion());
            row.setAnswer(qa.getAnswer());
            String similarQuestion = similarMap.getOrDefault(qa.getQaNo(), new ArrayList<>()).stream()
                    .map(AiKnowSimilarQuestionPo::getSimilarQuestion)
                    .collect(Collectors.joining("\n"));
            row.setSimilarQuestion(similarQuestion);
            return row;
        }).toList();
        log.info("导出QA数{}", rowList.size());
        //导出至Excel并上传至OSS
        String excelUrl;
        File tempFile = null;
        try {

            String fileKey = "问答话术库导出记录" + DateUtil.format(new Date(), PURE_DATE_FORMAT) + ".xlsx";
            tempFile = File.createTempFile(fileKey, ".xlsx");
            log.info("创建了临时文件{}存储excel,位于{}", fileKey, tempFile.getAbsolutePath());
            EasyExcel.write(tempFile.getPath(), QARow.class)
                    .sheet("问答话术库").doWrite(rowList);

            ossService.uploadFile(fileKey, tempFile);
            excelUrl = ossService.getFileUrl(fileKey);

        } catch (Exception e) {
            log.error("导出已复核违规列表失败,入参{}", JSON.toJSONString(req), e);
            throw new BizException("导出问答话术库失败");
        } finally {
            if (tempFile != null) {
                boolean delete = tempFile.delete();
                log.info("删除临时文件Excel:{}", delete ? "成功" : "失败");
            }
        }
        return new ExportQABo(excelUrl);
    }

    @Override
    @OpenLog("分页查询QA")
    public PageResult<PageQueryQABo> pageQuery(PageQAQuery req) {
        PageQueryQAPo pageQueryQAPo = Converter.INSTANCE.to(req);
        pageQueryQAPo.setKeyword(req.getKeyword());
        PageResult<AiKnowQaPo> pageResult = DBPageUtils.pageQuery(pageQueryQAPo, aiKnowQaMapper::page);
        return PageResult.map(pageResult, Converter.INSTANCE::to1);
    }

    @Override
    @OpenLog("批量更新QA分组")
    public void batchMoveQa(BatchMoveQaCommand req) {
        AiKnowGroupPo aiKnowGroupPo = getAiKnowGroupPo(req.getProjectId(), req.getGroupNo());
        AiKnowQaPo updatePo = new AiKnowQaPo();
        updatePo.setGroupNo(aiKnowGroupPo.getGroupNo());
        aiKnowQaMapper.updateByQaNoList(updatePo, req.getQaNoList());
    }

    @Override
    @OpenLog("批量删除QA")
    public void batchDeleteQa(BatchDeleteQaCommand req) {

        List<String> qaNoList = req.getQaNoList();
        String projectId = req.getProjectId();
        List<AiKnowQaPo> list = aiKnowQaMapper.selectByQaNoList(projectId, req.getQaNoList());

        if (CollectionUtil.isEmpty(list)) {
            log.warn("QA不存在,无需删除");
            return;
        }

        //批量删除本地QA
        aiKnowQaMapper.logicDeleteByQaNoList(projectId, qaNoList);

        //批量删除本地相似问题
        aiKnowSimilarQuestionMapper.logicDeleteByQaNo(qaNoList);

        //删除远程QA
        KnowledgeBaseIdConfig config = getAiKnowConfigPos(projectId);
        if (StrUtil.isBlank(config.getQuestionId()) || StrUtil.isBlank(config.getQaId())) {
            log.error("知识库ID不存在,无法删除QA,入参{}", JSON.toJSONString(req));
            throw new BizException("删除QA失败");
        }

        try {
            for (AiKnowQaPo aiKnowQaPo : list) {
                //删除问题文档
                difyDatasetsClient.deleteDocument(config.getQuestionId(), aiKnowQaPo.getExternalQuestionId());
                //删除QA文档
                difyDatasetsClient.deleteDocument(config.getQaId(), aiKnowQaPo.getExternalQaId());
            }
        } catch (Exception e) {
            log.error("删除QA失败", e);
            throw new BizException("删除QA失败");
        }
    }

    /**
     * 批量添加到远程知识库
     */
    private void batchAddToRemoteKnowledge(String projectId, List<QAProcessData> processDataList) {
        if (CollectionUtil.isEmpty(processDataList)) {
            return;
        }

        // 获取知识库配置
        KnowledgeBaseIdConfig knowledgeBaseIdConfig = getAiKnowConfigPos(projectId);

        // 使用并行流提高远程API调用效率
        List<CompletableFuture<Void>> futures = processDataList.parallelStream()
                .map(processData -> CompletableFuture.runAsync(() -> {
                    try {
                        AddQACommand qaCommand = processData.getAddQACommand();

                        // 构建问题内容
                        StringBuilder externalQuestion = new StringBuilder(QUESTION_PREFIX + qaCommand.getQuestion() + SWITCH_LINE);
                        for (String similarQuestion : qaCommand.getSimilarQuestionList()) {
                            externalQuestion.append(QUESTION_PREFIX).append(similarQuestion).append(SWITCH_LINE);
                        }

                        // 添加到远程问题库
                        String externalQuestionId = addDocument2Knowledge(qaCommand, knowledgeBaseIdConfig.getQuestionId(), externalQuestion, KnowContentTypeEnum.QUESTION);

                        // 构建QA内容
                        externalQuestion.append(SWITCH_LINE).append(ANSWER_PREFIX).append(qaCommand.getAnswer());
                        String externalQaId = addDocument2Knowledge(qaCommand, knowledgeBaseIdConfig.getQaId(), externalQuestion, KnowContentTypeEnum.QA);

                        // 设置外部ID
                        processData.setExternalQuestionId(externalQuestionId);
                        processData.setExternalQaId(externalQaId);

                    } catch (Exception e) {
                        log.error("批量添加到远程知识库失败，问题: {}", processData.getAddQACommand().getQuestion(), e);
                        throw new RuntimeException("添加到远程知识库失败: " + processData.getAddQACommand().getQuestion(), e);
                    }
                }))
                .toList();

        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("批量添加到远程知识库异步任务执行失败", e);
            throw new BizException("批量添加到远程知识库失败");
        }

        // 批量更新本地库的外部文档ID
        List<AiKnowQaPo> updatePoList = processDataList.stream()
                .map(processData -> AiKnowQaPo.builder()
                        .qaNo(processData.getQaPo().getQaNo())
                        .externalQuestionId(processData.getExternalQuestionId())
                        .externalQaId(processData.getExternalQaId())
                        .build())
                .toList();

        // 批量更新外部ID
        for (AiKnowQaPo updatePo : updatePoList) {
            aiKnowQaMapper.updateByQaNo(updatePo);
        }

        log.info("批量添加到远程知识库完成，数量: {}", processDataList.size());
    }

    /**
     * 批量更新远程知识库
     */
    private void batchUpdateRemoteKnowledge(String projectId, List<QAEditProcessData> processDataList) {
        if (CollectionUtil.isEmpty(processDataList)) {
            return;
        }

        // 获取知识库配置
        KnowledgeBaseIdConfig knowledgeBaseIdConfig = getAiKnowConfigPos(projectId);
        if (StrUtil.isBlank(knowledgeBaseIdConfig.getQuestionId()) || StrUtil.isBlank(knowledgeBaseIdConfig.getQaId())) {
            log.warn("远程知识库配置不存在");
            throw new BizException("远程知识库配置不存在");
        }

        // 使用并行流提高远程API调用效率
        List<CompletableFuture<Void>> futures = processDataList.parallelStream()
                .map(processData -> CompletableFuture.runAsync(() -> {
                    try {
                        EditQACommand editCommand = processData.getEditCommand();
                        AiKnowQaPo existingQa = processData.getExistingQa();

                        // 构建问题内容
                        StringBuilder externalQuestion = new StringBuilder(QUESTION_PREFIX + editCommand.getQuestion() + SWITCH_LINE);
                        List<String> similarQuestionList = editCommand.getSimilarQuestionList();
                        if (CollectionUtil.isNotEmpty(similarQuestionList)) {
                            for (String similarQuestion : similarQuestionList) {
                                externalQuestion.append(QUESTION_PREFIX).append(similarQuestion).append(SWITCH_LINE);
                            }
                        }

                        // 更新远程问题库
                        UpdateDocumentByTextRequest updateRequest = new UpdateDocumentByTextRequest();
                        updateRequest.setName(editCommand.getQuestion());
                        updateRequest.setText(externalQuestion.toString());
                        difyDatasetsClient.updateDocumentByText(knowledgeBaseIdConfig.getQuestionId(), existingQa.getExternalQuestionId(), updateRequest);

                        // 构建QA内容并更新QA库
                        externalQuestion.append(SWITCH_LINE).append(ANSWER_PREFIX).append(editCommand.getAnswer());
                        updateRequest.setText(externalQuestion.toString());
                        difyDatasetsClient.updateDocumentByText(knowledgeBaseIdConfig.getQaId(), existingQa.getExternalQaId(), updateRequest);

                    } catch (Exception e) {
                        log.error("批量更新远程知识库失败，问题: {}", processData.getEditCommand().getQuestion(), e);
                        throw new RuntimeException("更新远程知识库失败: " + processData.getEditCommand().getQuestion(), e);
                    }
                }))
                .toList();

        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("批量更新远程知识库异步任务执行失败", e);
            throw new BizException("批量更新远程知识库失败");
        }

        log.info("批量更新远程知识库完成，数量: {}", processDataList.size());
    }
}
