package com.jd.qf.ai.biz.core.service.know.qa;

import com.jd.qf.ai.biz.core.api.know.qa.bo.EditQACommand;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo;
import lombok.Builder;
import lombok.Data;

/**
 * QA编辑处理数据
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Data
@Builder
public class QAEditProcessData {
    
    /**
     * 编辑QA命令
     */
    private EditQACommand editCommand;
    
    /**
     * 现有QA实体对象
     */
    private AiKnowQaPo existingQa;
}
