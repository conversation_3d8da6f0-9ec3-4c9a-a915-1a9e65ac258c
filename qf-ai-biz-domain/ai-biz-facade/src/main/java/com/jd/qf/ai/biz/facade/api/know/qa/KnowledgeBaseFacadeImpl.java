package com.jd.qf.ai.biz.facade.api.know.qa;

import com.jd.qf.ai.biz.api.knowledgebase.qa.KnowledgeBaseFacade;
import com.jd.qf.ai.biz.api.knowledgebase.qa.dto.*;
import com.jd.qf.ai.biz.api.knowledgebase.qa.req.BatchAddQAReq;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 知识库门面实现类
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Slf4j
@Service
public class KnowledgeBaseFacadeImpl implements KnowledgeBaseFacade {



    @Override
    public BizResponse<Void> createKnowledgeBase(CreateKnowledgeBaseReq req) {
        return null;
    }

    @Override
    public BizResponse<Void> addQA(AddQAReq req) {
        return null;
    }

    @Override
    public BizResponse<Void> batchAddQA(BatchAddQAReq req) {
        return null;
    }

    @Override
    public BizResponse<QADetailResp> queryQADetail(QueryQADetailReq req) {
        return null;
    }

    @Override
    public BizResponse<Void> editQA(EditQAReq req) {
        return null;
    }

    @Override
    public BizResponse<Void> importQA(ImportQAReq req) {
        return null;
    }

    @Override
    public BizResponse<ExportQAResp> exportQA(PageQueryQAReq req) {
        return null;
    }

    @Override
    public BizResponse<PageResult<PageQueryQAResp>> pageQuery(PageQueryQAReq req) {
        return null;
    }

    @Override
    public BizResponse<Void> batchMoveQa(BatchMoveQaReq req) {
        return null;
    }

    @Override
    public BizResponse<Void> batchDeleteQa(BatchDeleteQaReq req) {
        return null;
    }
}
