package com.jd.qf.ai.biz.infrastructure.dao.mapper;

import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.PageQueryQAPo;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 知识库问答表Mapper接口
 */
public interface AiKnowQaMapper {

    /**
     * 插入知识库问答
     *
     * @param aiKnowQaPo 知识库问答实体
     * @return 影响的行数
     */
    int insert(AiKnowQaPo aiKnowQaPo);

    /**
     * 根据ID更新知识库问答
     *
     * @param aiKnowQaPo 知识库问答实体
     * @return 影响的行数
     */
    int updateById(AiKnowQaPo aiKnowQaPo);

    int updateByQaNo(AiKnowQaPo aiKnowQaPo);

    /**
     * 根据ID删除知识库问答
     *
     * @param id 主键ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识库问答
     *
     * @param id 主键ID
     * @return 知识库问答实体
     */
    AiKnowQaPo selectById(@Param("id") Long id);

    /**
     * 根据问答编码查询知识库问答
     *
     * @param qaNo 问答编码
     * @return 知识库问答实体
     */
    AiKnowQaPo selectByQaNo(@Param("qaNo") String qaNo);

    /**
     * 查询所有知识库问答
     *
     * @return 知识库问答实体列表
     */
    List<AiKnowQaPo> selectAll();

    /**
     * 根据项目ID查询知识库问答列表
     *
     * @param projectId 项目ID
     * @return 知识库问答实体列表
     */
    List<AiKnowQaPo> selectByProjectId(@Param("projectId") String projectId);

    /**
     * 根据分组编码查询知识库问答列表
     *
     * @param groupNo 分组编码
     * @return 知识库问答实体列表
     */
    List<AiKnowQaPo> selectByGroupNo(@Param("groupNo") String groupNo);

    /**
     * 根据项目ID和问题MD5查询知识库问答
     *
     * @param projectId 项目ID
     * @param questionMd5 问题MD5
     * @return 知识库问答实体
     */
    AiKnowQaPo selectByProjectIdAndQuestionMd5(@Param("projectId") String projectId, @Param("questionMd5") String questionMd5);

    AiKnowQaPo selectByProjectIdAndQaNo(@Param("projectId") String projectId, @Param("qaNo") String qaNo);

    void logicDeleteByQaNoList(@Param("projectId") String projectId, @Param("qaNoList") List<String> qaNoList);

    void updateByQaNoList(@Param("po") AiKnowQaPo updatePo, @Param("qaNoList") List<String> qaNoList);

    List<AiKnowQaPo> page(PageQueryQAPo pageQueryQAPo);

    List<AiKnowQaPo> selectByQaNoList(@Param("projectId") String projectId, @Param("qaNoList") List<String> qaNoList);

    List<AiKnowQaPo> selectByProjectIdAndMd5List(@Param("projectId") String projectId, @Param("questionMd5List") List<String> questionMd5List);

    /**
     * 批量插入知识库问答
     *
     * @param qaList 知识库问答实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("qaList") List<AiKnowQaPo> qaList);
}
