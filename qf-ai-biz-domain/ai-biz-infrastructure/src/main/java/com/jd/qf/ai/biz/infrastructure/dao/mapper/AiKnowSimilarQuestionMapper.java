package com.jd.qf.ai.biz.infrastructure.dao.mapper;

import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowSimilarQuestionPo;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 知识库相似问题表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface AiKnowSimilarQuestionMapper {

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的行数
     */
    int insert(AiKnowSimilarQuestionPo record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 查询到的记录
     */
    AiKnowSimilarQuestionPo selectByPrimaryKey(Long id);

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的行数
     */
    int updateByPrimaryKey(AiKnowSimilarQuestionPo record);

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除的行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据问答编码和相似问题MD5查询记录
     *
     * @param qaNo 问答编码
     * @param similarQuestionMd5List 相似问题MD5
     * @return 查询到的记录列表
     */
    List<AiKnowSimilarQuestionPo> selectByQaNoAndMd5(@Param("qaNo") String qaNo, @Param("similarQuestionMd5List") List<String> similarQuestionMd5List);

    List<AiKnowSimilarQuestionPo> selectByQaNo(String qaNo);

    void logicDeleteByQaNo(@Param("qaNoList") List<String> qaNoList);

    List<AiKnowSimilarQuestionPo> selectByQaList(@Param("projectId") String projectId, @Param("qaNoList") List<String> qaNoList);

    /**
     * 批量插入相似问题
     *
     * @param similarQuestionList 相似问题实体列表
     * @return 插入的行数
     */
    int batchInsert(@Param("similarQuestionList") List<AiKnowSimilarQuestionPo> similarQuestionList);
}
